include:
  - local: .gitlab/ci/dws-dependencies.yml

tests:unit:
  extends: .poetry
  stage: test
  needs:
    - install
  script:
    - make test-coverage-ci
  coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  artifacts:
    when: always
    expire_in: 1 weeks
    reports:
      junit:
        - .test-reports/*.xml
      coverage_report:
        coverage_format: cobertura
        path: .test-reports/coverage.xml
  variables:
    # Enabling debug mode of asyncio so that the test fails in the following cases:
    # - asyncio checks for coroutines that were not awaited and logs them; this mitigates the “forgotten await” pitfall.
    # - Many non-threadsafe asyncio APIs (such as loop.call_soon() and loop.call_at() methods) raise an exception if they are called from a wrong thread.
    PYTHONASYNCIODEBUG: "True"

tests:integration:
  extends: .poetry
  stage: test
  needs:
    - build-docker-model-gateway
  services:
    - name: ${TARGET_IMAGE}
      alias: ai-gateway
  variables:
    AIGW_SELF_SIGNED_JWT__SIGNING_KEY: "-----<PERSON><PERSON><PERSON> PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC+ErJOBYMe4/d5\nlcVtqnQEazhGYqyefHQNtfpzQyb/WuPJa5BZu68KeS0fWKcZJluNk/jGXSb3cvei\n28wtnbBlEOdTTOzMpP217rLtoYdnzgJfO7DLj3tUL+JbQHj51bLwgkrHlALFDoUt\nzWaOrs+WO6QVLnIsl+YZ6CrKpc9fXLtlvZp0yYuic1BxKCw8Q2lWjMhj6w1fubwM\ne3MsTMcgBV5ek3twY/vHkU/Q2gjniLZSTBmzaXrHjfbS1au2vQBTsKmtPDATrnTU\nVLxLvAtb7p3bZAZObIj4oDi+2qGQGDoWVaiGYXfRquKHdle7hBLwuvOlmVGAdfHd\nN18QO9kbAgMBAAECggEAQpdJScVrnThv/PRocVXtBJlN43bxlCr0+9K8NoKZ2I/O\ny0P4lD+JgUlX8in8jPafz7e+SjzbiIkKUk/gNkEPSjFEYi5Krh3F12YT4OYdCtSl\nhxjiBUc1BZcrHh415A2m/dUf/tEPsVs3dIcIPcfn6XHmuBcIIDBtiLkKuOmjf/xh\n/2l7QUHNC0Hn1STNvE5CscnqH0/s9dw2/KYhjpZWJsoNOAJ4PWgypxlftdUcspQK\nQL+FWrNL+aa1+nPeR0ArNw3nlkKnzcX3DmIHyD4az1IwrzHqMCh1zv16eqmCBYuN\nn2QrAAkAt+SFVEwjejwN/lNLMg4O6tyMvFnuOddp2QKBgQDhXgmOZgoJJYz7Vd0u\n/U3E4Ma+HysQ7TU3utidAke5YJXSms0UJrOFxgcRKserkXjD9wbyeUcMbrKRNTxm\nPgjGayVCqVc6T3kMHio8CvSs+RYt+jR7CnFIfQbQATR7c8nzy4Z/JNsW213SmXpj\n4S3TQWwGFSrF8h+A098cgFwNGQKBgQDX6IvIumrnQGiCJn8qQW3hKGfJwSl6/e4Z\nfC/o8uFzTtV1aL39AusDKAt4c5DsVUHCZLSH5acFGeeGt4dePpqnr0dpIb02ByGJ\na6tLJm2RwjaiXFOAsedOP12yqTZELi7rinKjVqG50eOvXuV40CVVP9qXzxE9e2DP\nQGe2ZFmqUwKBgFvsCkPNtOw0J8PgKt5erRjPgeDMP7mgtyMrD+1Cn9Sp45JKV7mP\na4v10K4c3+JH7JUprei6tNMKV8VIjIE7bkLYMxN0lMKQ5dOefiWNZm0jD/vi4QMK\nqFgjvuKaiziLauUIr6wucTpqcWNT/Iq+rv1K4u+8NH0Wm+jlAkzSwjkJAoGAG5Wd\nTk7q25KyB3bNpmNnm22jHPatywXoRp9EK7nkLewzf2WbaFjYF7YlCQWSzW7zENf2\n7KndldxCZUbLZ7IN5kCRmg/ycZWlpj34S4ikVQwAGOw8yuNvzuJvoSTXRwyzd+pf\nTRkDXo8/TKeOH8pQCr02u1B8PmOl8bSjy3y0q/sCgYAkdWZ6xLJeS9HlzuuXchl3\numZM4n88lad2yNoCu3aJs2fbVCGCiHyFsThluDU0KD3xE5+RZVpAMTErcCE3HqNK\nn1rbBNBVtUTKeu1Qkw0B1X813oH7omqmZVGJx+hceIKimjKWvD7hQlr90NPoFDFz\n0Laqissu1lxAspLYIulpWg==\n-----END PRIVATE KEY-----"
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    AIGW_AUTH__BYPASS_EXTERNAL_WITH_HEADER: true
    AIGW_MOCK_MODEL_RESPONSES: true
    AIGW_FASTAPI__API_PORT: 5052
    AI_GATEWAY_URL: "http://ai-gateway:5052"
    CI_DEBUG_SERVICES: true
  script:
    - make test-integration

tests:evaluation:
  extends: .poetry
  stage: test
  needs:
    - install
  script:
    - make eval
  when: manual

.test-base: &test-base
  stage: test
  trigger:
    project: gitlab-org/duo-workflow/testing/duo-workflow-tests
    strategy: depend

.test-variables: &test-variables
  LS_DATASET: "duo_workflow.swe-bench-verified-test.1"
  LANGCHAIN_PROJECT: "duo-workflow-ci-job"
  DW_SERVICE_REF: $CI_MERGE_REQUEST_SOURCE_BRANCH_SHA
  DW_CONCURRENCY: 3
  SWEBENCH_START: "true"

check-dws-dependencies:
  extends: .poetry
  stage: test
  needs:
    - install
  script:
    - make duo-workflow-service-dependencies
    - |
      if [ "$CI_PIPELINE_SOURCE" = "merge_request_event" ]; then
        (git diff --exit-code .gitlab/ci/dws-dependencies.yml || (echo "ERROR: The dependencies at .gitlab/ci/dws-dependencies.yml are out of date. Run 'make duo-workflow-service-dependencies' locally to update the file." && exit 1))
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

sanity-tests:
  # Runs SWE Bench tests on a small set of problems that are consistently resolved by Duo Workflow.
  <<: *test-base
  allow_failure: true # TODO: to be removed once swe-bench pipelines are stable enough
  needs:
    - check-dws-dependencies
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes: !reference [.duo-workflow-service-dependencies, changes]
  variables:
    <<: *test-variables
    LS_DATASET_SPLIT: "solved_consistently_sm"

regression-tests:
  <<: *test-base
  when: manual
  allow_failure: true
  variables:
    <<: *test-variables
    LS_DATASET_SPLIT: "validation_stratified_b06f4db4_p30"

.proto-variables:
  variables:
    DEBIAN_VERSION: "bookworm"
    RUBY_VERSION: "3.3"
    GO_VERSION: "1.24.6"  # datasource=golang-version depName=golang/go
    PYTHON_VERSION: "3.12.11"
    POETRY_VERSION: "2.0.1"

check-proto-go:
  extends: [.proto-variables]
  image: golang:${GO_VERSION}-${DEBIAN_VERSION}
  stage: test
  script:
    - apt update
    - apt install unzip # Needed by Makefile to download and install protoc
    - make gen-proto-go
    - '(git diff --exit-code || (echo "ERROR: There are changes after running make gen-proto-go. Run it locally and commit the changes." && exit 1))'
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - contract/**/*.proto
        - clients/gopb/**/*.go

check-proto-python:
  extends: [.proto-variables]
  image: python:${PYTHON_VERSION}-${DEBIAN_VERSION}
  stage: test
  script:
    - pip install poetry==${POETRY_VERSION}
    - make gen-proto-python
    - '(git diff --exit-code || (echo "ERROR: There are changes after running make gen-proto-python. Run it locally and commit the changes." && exit 1))'
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - contract/**/*.proto
        - contract/**/*.py
        - contract/**/*.pyi

check-proto-ruby:
  extends: [.proto-variables]
  image: ruby:${RUBY_VERSION}-${DEBIAN_VERSION}
  stage: test
  script:
    - make gen-proto-ruby
    - '(git diff --exit-code || (echo "ERROR: There are changes after running make gen-proto-ruby. Run it locally and commit the changes." && exit 1))'
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - contract/**/*.proto
        - clients/ruby/**/*.rb

check-proto-node:
  extends: [.proto-variables]
  image: node:18-${DEBIAN_VERSION}
  stage: test
  script:
    - make gen-proto-node
    - '(git diff --exit-code || (echo "ERROR: There are changes after running make gen-proto-node. Run it locally and commit the changes." && exit 1))'
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - contract/**/*.proto
        - clients/node/src/**/*.ts
        - clients/node/package.json
        - clients/node/package-lock.json

check-duo-workflow-docs:
  extends: .poetry
  stage: test
  needs:
    - install
  script:
    - make duo-workflow-docs
    - |
      if [ "$CI_PIPELINE_SOURCE" = "merge_request_event" ]; then
        (git diff --exit-code docs/duo_workflow_service_graphs.md || (echo "ERROR: The documentation at docs/duo_workflow_service_graphs.md is out of date. Run 'make duo-workflow-docs' locally to generate updated documentation." && exit 1))
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - duo_workflow_service/**/*
        - scripts/generate_graph_docs.py
        - docs/duo_workflow_service_graphs.md
