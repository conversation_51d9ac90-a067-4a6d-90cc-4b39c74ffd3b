---
name: CI/CD job identifier parser for Duo Chat
model:
  config_file: claude_3_7
  params:
    model_class_provider: anthropic
    temperature: 0.0
    max_tokens: 2_048
    max_retries: 1
unit_primitives:
  - ask_build
prompt_template:
  system: |
    {% include 'chat/build_reader/system/1.0.0.jinja' %}
  user: |
    {% include 'chat/build_reader/user/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/build_reader/assistant/1.0.0.jinja' %}
params:
  timeout: 60
