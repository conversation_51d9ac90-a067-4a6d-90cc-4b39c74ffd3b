---
name: GPT Issue reader agent
model:
  name: gpt
  params:
    model_class_provider: litellm
    temperature: 0.1
    max_tokens: 2_048
    max_retries: 1
unit_primitives:
  - ask_issue
prompt_template:
  system: |
    {% include 'chat/issue_reader/system/1.0.0.jinja' %}
  user: |
    {% include 'chat/issue_reader/user/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/issue_reader/assistant/1.0.0.jinja' %}
params:
  timeout: 60
