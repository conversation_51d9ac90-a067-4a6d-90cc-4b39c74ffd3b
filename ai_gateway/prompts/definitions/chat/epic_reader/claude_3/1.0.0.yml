---
name: <PERSON> 3 Epic reader agent
model:
  config_file: conversation_performant
  params:
    model_class_provider: litellm
    temperature: 0.1
    max_tokens: 2_048
unit_primitives:
  - ask_epic
prompt_template:
  system: |
    {% include 'chat/epic_reader/system/1.0.0.jinja' %}
  user: |
    {% include 'chat/epic_reader/user/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/epic_reader/assistant/1.0.0.jinja' %}
params:
  timeout: 60
