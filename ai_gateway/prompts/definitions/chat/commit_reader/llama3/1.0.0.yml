---
name: Llama3 Commit Reader agent
model:
  name: llama3
  params:
    model_class_provider: litellm
    temperature: 0.0
    max_tokens: 2_048
    max_retries: 1
unit_primitives:
  - duo_chat
prompt_template:
  system: |
    {% include 'chat/commit_reader/system/1.0.0.jinja' %}
  user: |
    {% include 'chat/commit_reader/user/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/commit_reader/assistant/1.0.0.jinja' %}
params:
  timeout: 60
