---
name: <PERSON> 3 Commit Reader agent
model:
  config_file: conversation_performant
  params:
    model_class_provider: litellm
    max_tokens: 2_048
unit_primitives:
  - duo_chat
prompt_template:
  system: |
    {% include 'chat/commit_reader/system/1.0.0.jinja' %}
  user: |
    {% include 'chat/commit_reader/user/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/commit_reader/assistant/1.0.0.jinja' %}
params:
  timeout: 60
