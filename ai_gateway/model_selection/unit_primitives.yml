# see docs/model_selection for documentation
configurable_unit_primitives:
  - feature_setting: "duo_chat"
    unit_primitives:
      - "ask_build"
      - "ask_commit"
    default_model: "claude_sonnet_3_7_20250219"
    selectable_models:
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "code_generations"
    unit_primitives:
      - "generate_code"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
      - "gemini_2_5_flash_vertex"
  - feature_setting: "code_completions"
    unit_primitives:
      - "complete_code"
    default_model: "codestral_2501_fireworks"
    selectable_models:
      # Make sure to also add the following models in `KindGitLabModel`
      # and `KindUseCase.CODE_COMPLETIONS`
      - "codestral_2501_fireworks"
      - "codestral_2501_vertex"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_fix_code"
    unit_primitives:
      - "fix_code"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_refactor_code"
    unit_primitives:
      - "refactor_code"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_write_tests"
    unit_primitives:
      - "write_tests"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_explain_code"
    unit_primitives:
      - "explain_code"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "generate_commit_message"
    unit_primitives:
      - "generate_commit_message"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "summarize_new_merge_request"
    unit_primitives:
      - "summarize_new_merge_request"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
      - "claude_sonnet_4_20250514"
  - feature_setting: "summarize_review"
    unit_primitives:
      - "summarize_review"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "resolve_vulnerability"
    unit_primitives:
      - "resolve_vulnerability"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_explain_vulnerability"
    unit_primitives:
      - "explain_vulnerability"
    default_model: "claude_sonnet_3_7_20250219"
    selectable_models:
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
      - "claude_3_haiku_20240307"
  - feature_setting: "code_suggestions"
    unit_primitives:
      - "generate_code"
      - "complete_code"
    default_model: "claude_sonnet_3_7_20250219"
    selectable_models:
      - "claude_sonnet_3_7_20250219"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "glab_ask_git_command"
    unit_primitives:
      - "glab_ask_git_command"
    default_model: "claude_3_5_haiku_20241022"
    selectable_models:
      - "claude_3_haiku_20240307"
      - "claude_3_5_haiku_20241022"
      - "claude_3_5_sonnet_20240620"
  - feature_setting: "duo_chat_troubleshoot_job"
    unit_primitives:
      - "troubleshoot_job"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_3_sonnet_20240229"
      - "claude_3_5_sonnet_20240620"
      - "claude_sonnet_3_7_20250219"
      - "claude_sonnet_4_20250514"
  - feature_setting: "review_merge_request"
    unit_primitives:
      - "review_merge_request"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_3_5_sonnet_20240620"
      - "claude_sonnet_3_7_20250219"
      - "claude_sonnet_4_20250514"
  - feature_setting: "duo_generate_issue_description"
    unit_primitives:
      - "generate_issue_description"
    default_model: "claude_3_5_sonnet_20240620"
    selectable_models:
      - "claude_3_5_sonnet_20240620"
      - "claude_sonnet_4_20250514"
  - feature_setting: "duo_chat_summarize_comments"
    unit_primitives:
      - "summarize_comments"
    default_model: "claude_sonnet_4_20250514"
    selectable_models:
      - "claude_3_5_sonnet_20240620"
      - "claude_sonnet_3_7_20250219"
      - "claude_sonnet_4_20250514"
      - "claude_sonnet_4_20250514_vertex"
